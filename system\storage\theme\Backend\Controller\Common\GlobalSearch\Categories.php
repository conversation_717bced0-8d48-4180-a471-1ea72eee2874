<?php

namespace Theme25\Backend\Controller\Common\GlobalSearch;

class Categories extends \Theme25\ControllerSubMethods {

    /**
     * Унифицирано търсене в категории с кеширане
     */
    public function search($query, $page = 1, $limit = 20, $extendedSearch = 0) {
        // Проверяваме кеша първо
        // $cacheKey = $this->modelSearch->generateCacheKey('categories_search', $query . '_ext_' . $extendedSearch . '_p' . $page . '_l' . $limit, $limit);
        // $cachedResults = $this->modelSearch->getCachedResults($cacheKey);

        // if ($cachedResults !== null) {
        //     return $cachedResults;
        // }

        $offset = ($page - 1) * $limit;

        // Получаваме общия брой резултати
        $total = $this->getTotalCount($query, $extendedSearch);

        F()->log->developer( $query, __FILE__, __LINE__);
        F()->log->developer('Total count: ' . $total, __FILE__, __LINE__);

        if ($total == 0) {
            return [
                'results' => [],
                'total' => 0
            ];
        }

        // Ако няма кеширани резултати, извършваме търсенето
        $results = $this->performOptimizedSearch($query, $limit, $extendedSearch, $offset);

        F()->log->developer('Results count: ' . count($results), __FILE__, __LINE__);

        $searchResults = [
            'results' => $results,
            'total' => $total
        ];

        // Кешираме резултатите
        // $this->modelSearch->cacheResults($cacheKey, $searchResults);

        return $searchResults;
    }

    /**
     * Оптимизирано търсене с релевантност
     */
    private function performOptimizedSearch($query, $limit = 5, $extendedSearch = 0, $offset = 0) {
        try {
            // Подготвяме думите за търсене
            $words = $this->modelSearch->prepareSearchWords($query);
            if (empty($words)) {
                return [];
            }

            // При разширено търсене комбинираме точни и частични съвпадения
            if ($extendedSearch) {
                // Първо търсим точни съвпадения
                $exactResults = $this->searchExactMatches($words, $limit, 0); // Винаги започваме от 0 за точните

                // Ако имаме достатъчно точни резултати, прилагаме offset и limit
                if (count($exactResults) >= ($offset + $limit)) {
                    return array_slice($exactResults, $offset, $limit);
                }

                // Ако точните резултати са по-малко от нужните, добавяме частични
                $exactCount = count($exactResults);
                $remainingNeeded = ($offset + $limit) - $exactCount;
                $partialOffset = max(0, $offset - $exactCount);

                $partialResults = $this->searchPartialMatches($words, $remainingNeeded, $exactResults, $partialOffset);

                // Обединяваме резултатите
                $allResults = array_merge($exactResults, $partialResults);

                // Прилагаме offset и limit върху комбинираните резултати
                return array_slice($allResults, $offset, $limit);
            } else {
                // При обикновено търсене връщаме само точните съвпадения
                return $this->searchExactMatches($words, $limit, $offset);
            }

        } catch (Exception $e) {
            return [];
        }
    }



    /**
     * Търси точни съвпадения в категории
     */
    private function searchExactMatches($words, $limit, $offset = 0) {
        $language_id = $this->getLanguageId();
        $results = [];

        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));

            $sql = "
                SELECT DISTINCT
                    c.category_id,
                    cd.name,
                    c.status,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "category c
                LEFT JOIN
                    " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
                WHERE
                    cd.language_id = '" . (int)$language_id . "'
                    AND LOWER(cd.name) like ('%{$escapedWord}%')
                ORDER BY
                    cd.name ASC
                LIMIT " . (int)$offset . ", " . (int)$limit;


            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $categoryId = $row['category_id'];
                if (!isset($results[$categoryId])) {
                    $results[$categoryId] = [
                        'category_id' => $row['category_id'],
                        'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                        'status' => $row['status'],
                        'relevance' => 100
                    ];
                }
            }

            if (count($results) >= $limit) {
                break;
            }
        }

        return array_values($results);
    }

    /**
     * Търси частични съвпадения в категории
     */
    private function searchPartialMatches($words, $limit, $excludeResults = [], $offset = 0) {
        $language_id = $this->getLanguageId();
        $results = [];
        $excludeIds = array_column($excludeResults, 'category_id');

        // Строим условията за търсене
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = ["LOWER(cd.name) LIKE '%{$escapedWord}%'"];

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(cd.name) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Добавяме изключване на вече намерените категории
        if (!empty($excludeIds)) {
            $excludeList = implode(',', array_map('intval', $excludeIds));
            $whereCondition .= " AND c.category_id NOT IN ({$excludeList})";
        }

        $sql = "
            SELECT DISTINCT
                c.category_id,
                cd.name,
                c.status,
                'partial' as match_type
            FROM
                " . DB_PREFIX . "category c
            LEFT JOIN
                " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
            WHERE
                cd.language_id = '" . (int)$language_id . "'
                AND ({$whereCondition})
            ORDER BY
                cd.name ASC
            LIMIT " . (int)$offset . ", " . (int)$limit;

        $query_result = $this->db->query($sql);

        foreach ($query_result->rows as $row) {
            $results[] = [
                'category_id' => $row['category_id'],
                'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                'status' => $row['status'],
                'relevance' => 50
            ];
        }

        return $results;
    }

    /**
     * Основна логика за търсене
     */
    private function performSearch($query, $limit = 5, $offset = 0) {
        $results = [];

        try {
            // Подготвяме търсещите условия
            $searchConditions = $this->buildSearchConditions($query);

            $language_id = $this->getLanguageId();

            $sql = "
                SELECT
                    c.category_id,
                    cd.name,
                    cd.description,
                    c.status,
                    c.sort_order,
                    c.image
                FROM
                    " . DB_PREFIX . "category c
                LEFT JOIN
                    " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
                WHERE
                    cd.language_id = '{$language_id}'
                    AND ({$searchConditions})
                ORDER BY
                    cd.name ASC
                LIMIT {$limit}
            ";

            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'category_id' => $row['category_id'],
                    'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                    'description' => strip_tags(html_entity_decode($row['description'], ENT_QUOTES, 'UTF-8')),
                    'status' => $row['status'],
                    'image' => $row['image']
                ];
            }

        } catch (Exception $e) {
            // Логиране на грешката
            error_log('Грешка при търсене в категории: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Получаване на общия брой резултати
     * Използва същата логика като performOptimizedSearch() за консистентност
     */
    private function getTotalCount($query, $extendedSearch = 0) {
        try {
            // Подготвяме думите за търсене
            $words = $this->modelSearch->prepareSearchWords($query);
            if (empty($words)) {
                return 0;
            }

            // Броим точните съвпадения
            $exactCount = $this->getExactMatchesCount($words);
            

            // Броим частичните съвпадения (без дублиране с точните)
            $partialCount = $this->getPartialMatchesCount($words, $exactCount > 0);

            return $exactCount + $partialCount;

        } catch (Exception $e) {
            error_log('Грешка при броене на категории: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Брои точните съвпадения
     */
    private function getExactMatchesCount($words) {
        $language_id = $this->getLanguageId();

        // Строим условията за точни съвпадения
        $exactConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));

            $wordConditions = [
                "LOWER(cd.name) LIKE ('%{$escapedWord}%')"
            ];

            if (is_numeric($word)) {
                $wordConditions[] = "c.category_id = '" . (int)$word . "'";
            }

            $exactConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        if (empty($exactConditions)) {
            return 0;
        }

        $whereCondition = implode(' AND ', $exactConditions);

        $sql = "
            SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "category c
            LEFT JOIN " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
            WHERE cd.language_id = '" . (int)$language_id . "'
            AND ({$whereCondition})";

        $result = $this->db->query($sql);
        return (int)$result->row['total'];
    }

    /**
     * Брои частичните съвпадения
     */
    private function getPartialMatchesCount($words, $hasExactMatches = false) {
        $language_id = $this->getLanguageId();

        // Строим условията за частични съвпадения
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = [
                "LOWER(cd.name) LIKE '%{$escapedWord}%'"
            ];

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(cd.name) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Ако имаме точни съвпадения, изключваме ги от частичните
        $excludeExact = '';
        if ($hasExactMatches) {
            $exactConditions = [];
            foreach ($words as $word) {
                $escapedWord = $this->db->escape(mb_strtolower($word));

                $wordConditions = [
                    "LOWER(cd.name) = '{$escapedWord}'"
                ];

                if (is_numeric($word)) {
                    $wordConditions[] = "c.category_id = '" . (int)$word . "'";
                }

                $exactConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
            }

            if (!empty($exactConditions)) {
                $excludeExact = ' AND NOT (' . implode(' AND ', $exactConditions) . ')';
            }
        }

        $sql = "
            SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "category c
            LEFT JOIN " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
            WHERE cd.language_id = '" . (int)$language_id . "'
            AND ({$whereCondition})
            {$excludeExact}";

        $result = $this->db->query($sql);
        return (int)$result->row['total'];
    }

    /**
     * Изграждане на търсещи условия с интелигентно търсене
     */
    private function buildSearchConditions($query) {
        $conditions = [];

        // Почистваме и разделяме заявката на думи
        $words = $this->modelSearch->prepareSearchWords($query);

        if (empty($words)) {
            return "1=0"; // Няма валидни думи за търсене
        }

        // За всяка дума създаваме условия
        foreach ($words as $word) {
            $wordConditions = [];

            // Оригиналната дума
            $escapedWord = $this->db->escape(mb_strtolower($word));

            // Транслитерирана версия
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            // Fuzzy варианти (за думи над 4 символа)
            $fuzzyVariants = $this->modelSearch->generateFuzzyVariants($word);

            // Търсене в различни полета
            $fields = ['cd.name', 'cd.description'];

            foreach ($fields as $field) {
                $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedWord}%'";

                if ($transliteratedWord !== $word) {
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedTranslit}%'";
                }

                // Добавяме fuzzy варианти
                foreach ($fuzzyVariants as $variant) {
                    $escapedVariant = $this->db->escape(mb_strtolower($variant));
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedVariant}%'";
                }
            }

            // Обединяваме условията за тази дума с OR
            $conditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        // Обединяваме всички думи с AND (всички думи трябва да се намират)
        return implode(' AND ', $conditions);
    }


    /**
     * Рендира визуализацията на списъка с категории
     */
    public function renderItems($results, $withPagination = false, $query = '', $page = 1, $limit = 20, $total = 0, $extendedSearch = 0) {
        // Подготвяме данните за категориите
        $categories = [];
        foreach ($results as $category) {
            $categories[] = [
                'category_id' => $category['category_id'],
                'name' => $category['name'],
                'status' => ($category['status'] ?? 0) == 1 ? 'Активна' : 'Неактивна',
                'status_class' => ($category['status'] ?? 0) == 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800',
                'sort_order' => $category['sort_order'] ?? 0,
                'date_added' => $category['date_added'] ?? '',
                'parent_id' => $category['parent_id'] ?? 0,
                'view' => $this->getAdminLink('catalog/category/view', '&category_id=' . $category['category_id']),
                'edit' => $this->getAdminLink('catalog/category/edit', '&category_id=' . $category['category_id'])
            ];
        }

        // Подготвяме данните за шаблона
        $data = [
            'categories' => $categories,
            'with_pagination' => $withPagination,
            'query' => $query,
            'page' => $page,
            'limit' => $limit,
            'total' => $total
        ];

        // Пагинация ако е необходима
        if ($withPagination && $total > $limit) {
            $pagination = new \Theme25\Pagination();
            $pagination->total = $total;
            $pagination->page = $page;
            $pagination->limit = $limit;
            $pagination->url = $this->getAdminLink('common/globalsearch/categories', '&query=' . urlencode($query) . '&extended_search=' . $extendedSearch . '&page={page}');
            $pagination->setProductText('категории');
            $data['pagination'] = $pagination->render();
        }

        $this->setData($data);

        // Рендираме шаблона и връщаме HTML
        return $this->renderPartTemplate('common/global_search_categories');
    }
}
